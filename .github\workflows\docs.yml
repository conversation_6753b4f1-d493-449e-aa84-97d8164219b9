# Simple workflow for deploying static content to GitHub Pages
name: Docs

on:
  # Runs on pushes targeting the default branch
  push:
    branches:
      - main

  pull_request:
    branches:
      - main

  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

# Sets permissions of the GITHUB_TOKEN to allow deployment to GitHub Pages
permissions:
  contents: read
  pages: write
  id-token: write

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  build-04:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        version:
          [
            {
              ref: "${{github.ref}}",
              dest-dir: dev,
              uv-version: "0.7.13",
              sphinx-release-override: "dev",
              poe-dir: ".",
            },
            {
              ref: "python-v0.7.1.post1",
              dest-dir: stable,
              uv-version: "0.7.13",
              sphinx-release-override: "stable",
              poe-dir: ".",
            },
            {
              ref: "v0.4.0.post1",
              dest-dir: "0.4.0",
              uv-version: "0.5.13",
              sphinx-release-override: "",
              poe-dir: "./packages/autogen-core",
            },
            {
              ref: "v0.4.1",
              dest-dir: "0.4.1",
              uv-version: "0.5.13",
              sphinx-release-override: "",
              poe-dir: "./packages/autogen-core",
            },
            {
              ref: "v0.4.2",
              dest-dir: "0.4.2",
              uv-version: "0.5.13",
              sphinx-release-override: "",
              poe-dir: "./packages/autogen-core",
            },
            {
              ref: "v0.4.3",
              dest-dir: "0.4.3",
              uv-version: "0.5.13",
              sphinx-release-override: "",
              poe-dir: "./packages/autogen-core",
            },
            {
              ref: "v0.4.4",
              dest-dir: "0.4.4",
              uv-version: "0.5.13",
              sphinx-release-override: "",
              poe-dir: "./packages/autogen-core",
            },
            {
              ref: "python-v0.4.5",
              dest-dir: "0.4.5",
              uv-version: "0.5.13",
              sphinx-release-override: "",
              poe-dir: "./packages/autogen-core",
            },
            {
              ref: "python-v0.4.6",
              dest-dir: "0.4.6",
              uv-version: "0.5.13",
              sphinx-release-override: "",
              poe-dir: "./packages/autogen-core",
            },
            {
              ref: "python-v0.4.7",
              dest-dir: "0.4.7",
              uv-version: "0.5.13",
              sphinx-release-override: "",
              poe-dir: "./packages/autogen-core",
            },
            {
              ref: "python-v0.4.8",
              dest-dir: "0.4.8",
              uv-version: "0.5.13",
              sphinx-release-override: "",
              poe-dir: "./packages/autogen-core",
            },
            {
              ref: "python-v0.4.9-website",
              dest-dir: "0.4.9",
              uv-version: "0.5.13",
              sphinx-release-override: "",
              poe-dir: "./packages/autogen-core",
            },
            {
              ref: "python-v0.5.1",
              dest-dir: "0.5.1",
              uv-version: "0.5.13",
              sphinx-release-override: "",
              poe-dir: "./packages/autogen-core",
            },
            {
              ref: "python-v0.5.2",
              dest-dir: "0.5.2",
              uv-version: "0.5.13",
              sphinx-release-override: "",
              poe-dir: "./packages/autogen-core",
            },
            {
              ref: "python-v0.5.3",
              dest-dir: "0.5.3",
              uv-version: "0.5.13",
              sphinx-release-override: "",
              poe-dir: "./packages/autogen-core",
            },
            {
              ref: "python-v0.5.4",
              dest-dir: "0.5.4",
              uv-version: "0.5.13",
              sphinx-release-override: "",
              poe-dir: "./packages/autogen-core",
            },
            {
              ref: "python-v0.5.5",
              dest-dir: "0.5.5",
              uv-version: "0.5.13",
              sphinx-release-override: "",
              poe-dir: "./packages/autogen-core",
            },
            {
              ref: "python-v0.5.6",
              dest-dir: "0.5.6",
              uv-version: "0.5.13",
              sphinx-release-override: "",
              poe-dir: "./packages/autogen-core",
            },
            {
              ref: "python-v0.5.7",
              dest-dir: "0.5.7",
              uv-version: "0.5.13",
              sphinx-release-override: "",
              poe-dir: "./packages/autogen-core",
            },
            {
              ref: "python-v0.6.1",
              dest-dir: "0.6.1",
              uv-version: "0.5.13",
              sphinx-release-override: "",
              poe-dir: "./packages/autogen-core",
            },
            {
              ref: "python-v0.6.2",
              dest-dir: "0.6.2",
              uv-version: "0.7.13",
              sphinx-release-override: "",
              poe-dir: "./packages/autogen-core",
            },
            {
              ref: "python-v0.6.4",
              dest-dir: "0.6.4",
              uv-version: "0.7.13",
              sphinx-release-override: "",
              poe-dir: ".",
            },
            {
              ref: "python-v0.7.1.post1",
              dest-dir: "0.7.1",
              uv-version: "0.7.13",
              sphinx-release-override: "",
              poe-dir: ".",
            },
          ]
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          lfs: "true"
          ref: ${{ matrix.version.ref }}
      - uses: astral-sh/setup-uv@v5
        with:
          enable-cache: true
          version: ${{ matrix.version.uv-version }}
      - uses: actions/setup-python@v5
        with:
          python-version: "3.11"
      - run: |
          uv sync --locked --all-extras
          source .venv/bin/activate
          poe --directory ${{ matrix.version.poe-dir }} docs-build
          mkdir -p docs-staging/${{ matrix.version.dest-dir }}/
          mv ${{ matrix.version.poe-dir }}/docs/build/* docs-staging/${{ matrix.version.dest-dir }}/
        working-directory: ./python
        env:
          PY_DOCS_DIR: ${{ matrix.version.dest-dir }}/
          PY_SWITCHER_VERSION: ${{ matrix.version.dest-dir }}
          SPHINX_RELEASE_OVERRIDE: ${{ matrix.version.sphinx-release-override }}
      - uses: actions/upload-artifact@v4
        with:
          path: "./python/docs-staging"
          name: "${{ matrix.version.dest-dir }}-docs"

  gen-redirects:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          lfs: "true"
      - uses: actions/setup-python@v5
        with:
          python-version: "3.11"
      - name: generate redirects
        run: |
          mkdir -p python/docs-staging/
          python python/docs/redirects/redirects.py python/docs-staging
      - uses: actions/upload-artifact@v4
        with:
          path: "./python/docs-staging"
          name: "redirects"

  gen-component-schema:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          lfs: "true"
          ref: ${{ matrix.version.ref }}
      - uses: astral-sh/setup-uv@v5
        with:
          enable-cache: true
      - uses: actions/setup-python@v5
        with:
          python-version: "3.11"
      - run: |
          uv sync --locked --all-extras
          source .venv/bin/activate
          mkdir -p docs-staging/schemas/
          gen-component-schema > docs-staging/schemas/component-schema-latest.json
        working-directory: ./python
      - uses: actions/upload-artifact@v4
        with:
          path: "./python/docs-staging"
          name: "component-schema"

  build-02:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: website
    steps:
      - uses: actions/checkout@v4
        with:
          lfs: true
          ref: "0.2"
      - uses: actions/setup-node@v4
        with:
          node-version: 18.x
      - name: setup python
        uses: actions/setup-python@v5
        with:
          python-version: "3.9"
      - name: pydoc-markdown install
        run: |
          python -m pip install --upgrade pip
          pip install docspec==2.2.1 docspec-python==2.2.1
          pip install pydoc-markdown pyyaml termcolor
          # Pin databind packages as version 4.5.0 is not compatible with pydoc-markdown.
          pip install databind.core==4.4.2 databind.json==4.4.2
      - name: pydoc-markdown run
        run: |
          pydoc-markdown
      - name: quarto install
        working-directory: ${{ runner.temp }}
        run: |
          wget -q https://github.com/quarto-dev/quarto-cli/releases/download/v1.5.23/quarto-1.5.23-linux-amd64.tar.gz
          tar -xzf quarto-1.5.23-linux-amd64.tar.gz
          echo "$(pwd)/quarto-1.5.23/bin/" >> $GITHUB_PATH
      - name: Process notebooks
        run: |
          python process_notebooks.py render
      - name: Build website
        run: |
          if [ -e yarn.lock ]; then
          yarn install --frozen-lockfile --ignore-engines
          yarn build
          elif [ -e package-lock.json ]; then
          npm ci
          npm run build
          else
          npm i --legacy-peer-deps
          npm run build
          fi

      - run: |
          mkdir -p artifact/0.2/
          cp -r build/* artifact/0.2/

      - uses: actions/upload-artifact@v4
        with:
          path: "website/artifact"
          name: "02-docs"

  build-04-dotnet:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          lfs: true
      - name: Setup .NET 8.0
        uses: actions/setup-dotnet@v4
        with:
          global-json-file: dotnet/global.json
      - run: dotnet tool update -g docfx
      - run: |
          docfx docs/dotnet/docfx.json
          mkdir -p build/dotnet/
          mv docs/dotnet/_site build/dotnet/dev
      - name: insert clarity snippet to *.html
        working-directory: build/dotnet/dev/
        shell: python
        run: |
          import os
          clarity_script = """
          <script type="text/javascript">
              (function(c,l,a,r,i,t,y){
                  c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
                  t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
                  y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
              })(window, document, "clarity", "script", "lnxpe6skj1");
          </script>
          """

          site_folder = '.'

          for root, dirs, files in os.walk(site_folder):
              for file in files:
                  if file.endswith('.html'):
                      html_path = os.path.join(root, file)

                      # insert the script into the html's head section
                      with open(html_path, 'r') as file:
                          html = file.read()
                          html = html.replace('</head>', clarity_script + '</head>')

                      with open(html_path, 'w') as file:
                          file.write(html)

                      print(f'Clarity script inserted into {html_path}')
      - uses: actions/upload-artifact@v4
        with:
          path: "build/"
          name: "dotnet-dev-docs"

  deploy:
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    runs-on: ubuntu-latest
    needs:
      [build-02, build-04, build-04-dotnet, gen-redirects, gen-component-schema]
    if: ${{ needs.build-02.result == 'success' && needs.build-04.result == 'success' && needs.gen-redirects.result == 'success' && github.ref == 'refs/heads/main' }}
    steps:
      - uses: actions/download-artifact@v4
        with:
          path: artifacts

      - name: Copy 02-docs
        run: |
          mkdir -p deploy/
          for dir in artifacts/*; do
            cp -r $dir/* deploy/
          done

      - name: Upload artifact
        uses: actions/upload-pages-artifact@v3
        with:
          path: "./deploy"
      - name: Deploy to GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v4
