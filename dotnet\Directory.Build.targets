<Project>

  <PropertyGroup>
    <ReadMePath>$(MSBuildProjectDirectory)\README.md</ReadMePath>
    <ReadMeExists Condition="Exists('$(ReadMePath)')">true</ReadMeExists>
    <PackageReadmeFile Condition="'$(PackageReadmeFile)' == '' And '$(ReadMeExists)' == 'true'">README.md</PackageReadmeFile>
  </PropertyGroup>

  <ItemGroup Condition="'$(ReadMeExists)' == 'true'">
    <None Include="$(ReadMePath)" Pack="true" PackagePath="\" />
  </ItemGroup>

</Project>
