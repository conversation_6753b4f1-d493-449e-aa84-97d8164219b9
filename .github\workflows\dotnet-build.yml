# This workflow will build a .NET project
# For more information see: https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-net

name: dotnet-ci

on:
  workflow_dispatch:
  pull_request:
    branches: [ "main", "staging" ]
  push:
    branches: [ "main", "staging" ]
  merge_group:
    types: [checks_requested]

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}-${{ github.head_ref }}
  cancel-in-progress: ${{ github.ref != 'refs/heads/main' || github.ref != 'refs/heads/dotnet' }}

permissions:
  contents: read
  packages: write

jobs:
  paths-filter:
    runs-on: ubuntu-latest
    permissions:
      contents: write
      pull-requests: write
      repository-projects: write
    outputs:
      hasChanges: ${{ steps.filter.outputs.dotnet == 'true'}}
    steps:
      - uses: actions/checkout@v4
      - uses: dorny/paths-filter@v2
        id: filter
        with:
          filters: |
            dotnet:
              - "dotnet/**"
              - "protos/**"
            workflows:
              - ".github/workflows/**"
      - name: dotnet has changes
        run: echo "dotnet has changes"
        if: steps.filter.outputs.dotnet == 'true'
      - name: workflows has changes
        run: echo "workflows has changes"
        if: steps.filter.outputs.workflows == 'true'

  build:
    name: Dotnet Build & Test
    needs: paths-filter
    if: needs.paths-filter.outputs.hasChanges == 'true'
    defaults:
      run:
        working-directory: dotnet
    strategy:
      fail-fast: false
      matrix:
        os: [ ubuntu-latest, macos-latest ]
        python-version: ["3.11"]
    runs-on: ${{ matrix.os }}
    timeout-minutes: 30
    steps:
    - uses: actions/checkout@v4
      with:
        lfs: true
    - uses: astral-sh/setup-uv@v5
      with:
        enable-cache: true
    - uses: actions/setup-python@v5
      with:
        python-version: "3.11"
    - run: uv sync --locked --all-extras
      working-directory: ./python
    - name: Prepare python venv
      run: |
        source ${{ github.workspace }}/python/.venv/bin/activate
    - name: Setup .NET 8.0
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: '8.0.x'
    - name: Setup .NET 9.0
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: '9.0.x'
    - name: Restore dependencies
      run: dotnet restore -bl
    - name: Format check
      run: |
        echo "Format check"
        echo "If you see any error in this step, please run 'dotnet format' locally to format the code."
        dotnet format --verify-no-changes -v diag --no-restore
    - name: Build
      run: |
        echo "Build AutoGen"
        dotnet build --no-restore --configuration Release -bl /p:SignAssembly=true
    - name: Unit Test V1
      run: dotnet test --no-build -bl --configuration Release --filter "Category=UnitV1"
    - name: Unit Test V2 (With Coverage)
      run: dotnet test --no-build -bl --configuration Release --filter "Category=UnitV2" --collect:"XPlat Code Coverage"
    - name: Install Dev Certs for GRPC
      if: matrix.os == 'ubuntu-latest'
      run: dotnet dev-certs https --trust
    - name: GRPC Tests (With Coverage)
      if: matrix.os == 'ubuntu-latest'
      run: dotnet test --no-build -bl --configuration Release --filter "Category=GRPC" --collect:"XPlat Code Coverage"
    - name: Generate & Merge Coverage Report
      if: matrix.os == 'ubuntu-latest'
      run: |
        # Install reportgenerator
        dotnet tool install -g dotnet-reportgenerator-globaltool || dotnet tool update -g dotnet-reportgenerator-globaltool
        # Ensure output directory exists
        mkdir -p ${{ github.workspace }}/dotnet/coverage-report
        # Merge all coverage reports and generate HTML + XML
        reportgenerator \
          -reports:${{ github.workspace }}/dotnet/**/TestResults/**/coverage.cobertura.xml \
          -targetdir:${{ github.workspace }}/dotnet/coverage-report \
          -reporttypes:"Cobertura;Html"
        ls -R ${{ github.workspace }}/dotnet/coverage-report
    - name: Upload Merged Coverage Report
      if: matrix.os == 'ubuntu-latest'
      uses: actions/upload-artifact@v4
      with:
        name: CodeCoverageReport
        path: ${{ github.workspace }}/dotnet/coverage-report/
        retention-days: 7
    - name: Upload Coverage to Codecov
      if: matrix.os == 'ubuntu-latest'
      uses: codecov/codecov-action@v5
      with:
        files: ${{ github.workspace }}/dotnet/coverage-report/*.xml
        flags: unittests
        name: dotnet-codecov
        fail_ci_if_error: true
        token: ${{ secrets.CODECOV_TOKEN }}

  integration-test:
    strategy:
      fail-fast: true
      matrix:
        os: [ ubuntu-latest]
        version: [ net8.0 ]
    needs: build
    defaults:
      run:
        working-directory: dotnet
    runs-on: ${{ matrix.os }}
    steps:
    - uses: actions/checkout@v4
      with:
          lfs: true
    - uses: astral-sh/setup-uv@v5
      with:
        enable-cache: true
    - uses: actions/setup-python@v5
      with:
        python-version: "3.11"
    - run: uv sync --locked --all-extras
      working-directory: ./python
    - name: Prepare python venv
      run: |
        source ${{ github.workspace }}/python/.venv/bin/activate
    - name: Setup .NET 8.0
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: '8.0.x'
    - name: Setup .NET 9.0
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: '9.0.x'
    - name: Install Temp Global.JSON
      run: |
        echo "{\"sdk\": {\"version\": \"9.0\"}}" > global.json
    - name: Install .NET Aspire workload
      run: dotnet workload install aspire
    - name: Install dev certs
      run:  dotnet --version && dotnet dev-certs https --trust
    - name: Restore dependencies
      run: |
        dotnet restore -bl
    - name: Build
      run: |
        echo "Build AutoGen"
        dotnet build --no-restore --configuration Release -bl /p:SignAssembly=true
    - name: Integration Test
      run: dotnet --version && dotnet test --no-build -bl --configuration Release --filter "Category=Integration"
    - name: Restore the global.json
      run: rm global.json && git checkout -- global.json

  aot-test: # this make sure the AutoGen.Core is aot compatible
    strategy:
      fail-fast: false  # ensures the entire test matrix is run, even if one permutation fails
      matrix:
        os: [ ubuntu-latest ]
        version: [ net8.0 ]
    needs: build
    defaults:
      run:
        working-directory: dotnet

    runs-on: ${{ matrix.os }}
    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0 # fetching all

    - name: Setup dotnet
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: '8.0.x'
    - name: Setup .NET 9.0
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: '9.0.x'

    - name: publish AOT testApp, assert static analysis warning count, and run the app
      shell: pwsh
      run: ./.tools/test-aot-compatibility.ps1 ${{ matrix.version }}
  openai-test:
    name: Run openai test
    runs-on: ubuntu-latest
    environment: dotnet
    defaults:
      run:
        working-directory: dotnet
    if: success() && (github.ref == 'refs/heads/main')
    needs: aot-test
    steps:
    - uses: actions/checkout@v4
      with:
        lfs: true
    - name: Set up Python 3.11
      uses: actions/setup-python@v5
      with:
        python-version: 3.11
    - name: Install jupyter and ipykernel
      run: |
        python -m pip install --upgrade pip
        python -m pip install jupyter
        python -m pip install ipykernel
    - name: list available kernels
      run: |
        python -m jupyter kernelspec list
    - uses: astral-sh/setup-uv@v5
      with:
        enable-cache: true
    - uses: actions/setup-python@v5
      with:
        python-version: "3.11"

    - name: Setup .NET 8.0
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: '8.0.x'
        global-json-file: dotnet/global.json
    - name: Setup .NET 9.0
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: '9.0.x'
    - name: Install dev certs
      run:  dotnet --version && dotnet dev-certs https --trust
    - name: Restore dependencies
      run: |
        dotnet restore -bl
    - name: Build
      run: |
        echo "Build AutoGen"
        dotnet build --no-restore --configuration Release -bl /p:SignAssembly=true
    - name: OpenAI Test
      run: dotnet test --no-build -bl --configuration Release --filter type!=integration
      env:
        AZURE_OPENAI_API_KEY: ${{ secrets.AZURE_OPENAI_API_KEY }}
        AZURE_OPENAI_ENDPOINT: ${{ secrets.AZURE_OPENAI_ENDPOINT }}
        AZURE_GPT_35_MODEL_ID: ${{ secrets.AZURE_GPT_35_MODEL_ID }}
        OEPNAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
    - name: Pack
      run: |
        echo "Create nightly build package"
        dotnet pack --no-build --configuration Release --output './output/nightly' -p:VersionSuffix=nightly-${{github.run_id}} -bl

        echo "Create release build package"
        dotnet pack --no-build --configuration Release --output './output/release' -bl

        echo "ls output directory"
        ls -R ./output
    - name: Upload package
      uses: actions/upload-artifact@v4
      with:
        name: nightly
        path: ./dotnet/output/nightly
    - name: Upload package
      uses: actions/upload-artifact@v4
      with:
        name: release
        path: ./dotnet/output/release
  publish:
    environment: dotnet-internal-feed
    name: Publish to nightly feeds
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: dotnet
    needs: openai-test
    steps:
    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: '6.0.x'
        source-url: https://devdiv.pkgs.visualstudio.com/DevDiv/_packaging/AutoGen/nuget/v3/index.json
      env:
        NUGET_AUTH_TOKEN: ${{ secrets.AZURE_DEVOPS_TOKEN }}
    - uses: actions/download-artifact@v4
      with:
        name: nightly
        path: ./dotnet/output/nightly
    - uses: actions/download-artifact@v4
      with:
        name: release
        path: ./dotnet/output/release
    - name: Publish nightly package to Azure Devops
      run: |
        echo "Publish nightly package to Azure Devops"
        echo "ls output directory"
        ls -R ./output/nightly
        dotnet nuget push --api-key AzureArtifacts ./output/nightly/*.nupkg --skip-duplicate
      env:
        AZURE_ARTIFACTS_FEED_URL: https://devdiv.pkgs.visualstudio.com/DevDiv/_packaging/AutoGen/nuget/v3/index.json
        NUGET_AUTH_TOKEN: ${{ secrets.AZURE_DEVOPS_TOKEN }}
      continue-on-error: true
    - name: Publish nightly package to github package
      run: |
        echo "Publish nightly package to github package"
        echo "ls output directory"
        ls -R ./output/nightly
        dotnet nuget push --api-key ${{ secrets.GITHUB_TOKEN }} --source "https://nuget.pkg.github.com/microsoft/index.json" ./output/nightly/*.nupkg --skip-duplicate
      continue-on-error: true
    - name: Publish nightly package to agentchat myget feed
      run: |
        echo "Publish nightly package to agentchat myget feed"
        echo "ls output directory"
        ls -R ./output/nightly
        dotnet nuget push --api-key ${{ secrets.MYGET_TOKEN }} --source "https://www.myget.org/F/agentchat/api/v3/index.json" ./output/nightly/*.nupkg --skip-duplicate
      env:
        MYGET_TOKEN: ${{ secrets.MYGET_TOKEN }}
      continue-on-error: true

