我想基于OpenAI Agents SDK开发一个可视化工作流设计工具。需求是：在网页上提供一个画布，用户可以拖放组件（对应SDK中的Agent、Runner等）并配置它们的参数（如name、instructions），组件之间可以建立连接关系。完成工作流设计后，系统能够生成对应的完整可用SDK代码。

请问实现这种拖拽式工作流设计工具的最佳前端技术栈是什么？需要支持：
1. 交互式画布操作
2. 组件拖放与连接
3. 参数配置面板
4. 根据可视化工作流生成代码

以下是我要实现的OpenAI Agents SDK示例代码：

# 基本示例
from agents import Agent, Runner

agent = Agent(name="Assistant", instructions="You are a helpful assistant")
result = Runner.run_sync(agent, "Write a haiku about recursion in programming.")
print(result.final_output)

# 多Agent协作示例
spanish_agent = Agent(name="Spanish agent", instructions="You only speak Spanish.")
english_agent = Agent(name="English agent", instructions="You only speak English")
triage_agent = Agent(
    name="Triage agent", 
    instructions="Handoff to the appropriate agent based on the language of the request.",
    handoffs=[spanish_agent, english_agent],
)

# 带工具函数的示例
@function_tool
def get_weather(city: str) -> str:
    return f"The weather in {city} is sunny."

agent = Agent(name="Hello world", instructions="You are a helpful agent.", tools=[get_weather])



# OpenAI Agents 可视化工作流设计器实现需求

我需要设计并实现一个可视化界面，让用户能够通过拖放组件方式创建OpenAI Agents工作流，并生成对应的代码。
项目需求如下：

## 1. 功能概述

创建一个基于Web的可视化编辑器，允许用户:
- 从组件库拖放不同类型的组件到画布上
- 通过连接线连接组件，建立关系
- 配置组件参数（如Agent的name、instructions等）
- 生成可运行的OpenAI Agents SDK代码

## 2. 技术栈

- **前端框架**: React 18+
- **工作流编辑器库**: React Flow
- **UI组件库**: 不限，可使用Ant Design、Material UI或自定义组件
- **状态管理**: React Context或Redux
- **构建工具**: Vite

## 3. 界面布局

应用界面分为以下几个主要区域:

1. **顶部导航栏**
   - 应用名称: "OpenAI Agents Workflow Designer"
   - 生成代码按钮
   - 可选: 保存/加载按钮

2. **左侧组件面板**
   - 可拖动的组件类型:
     - Agent节点
     - Runner节点
     - Function Tool节点
   - 每个组件有图标和名称
   - 底部可添加简要使用说明

3. **中央画布区域**
   - 可拖放区域，用于构建工作流
   - 支持平移和缩放
   - 节点之间可建立连接关系

4. **右侧属性面板**（可选）
   - 显示当前选中节点的详细属性
   - 提供属性编辑界面

5. **代码生成弹窗**
   - 展示生成的OpenAI Agents SDK代码
   - 提供复制按钮
   - 可选: 代码高亮显示

## 4. 组件设计

### 4.1 Agent节点

- **外观**: 圆角矩形，顶部蓝色边框
- **基本属性**:
  - name: 文本输入框
  - instructions: 多行文本输入框
  - handoff_description: 文本输入框（可选）
  - output_type: 下拉选择（无/自定义Pydantic模型）
- **扩展属性**:
  - handoffs: 显示已连接的其他Agent
  - tools: 显示已连接的Function Tools

### 4.2 Runner节点

- **外观**: 圆角矩形，顶部红色边框
- **基本属性**:
  - input: 文本输入框
  - 执行模式: 切换按钮(同步/异步)
  - context: 可选配置
- **注意**: Runner节点应连接到一个Agent节点

### 4.3 Function Tool节点

- **外观**: 圆角矩形，顶部黄色边框
- **基本属性**:
  - name: 文本输入框
  - parameters: 可添加/删除的参数列表
  - returnType: 下拉选择(str, int, float, bool, list, dict, None)
  - implementation: 代码编辑区

### 4.5 连接关系设计

- Agent → Agent: 表示handoff关系
- Function → Agent: 表示tool关系
- Agent → Runner: 表示执行关系

## 5. 代码生成器实现

代码生成器需要根据画布上的节点和连接关系，生成有效的OpenAI Agents SDK代码。生成的代码应包括:

1. 必要的导入语句（agents、asyncio等）
2. Pydantic模型定义（如果需要）
3. Function Tool 定义
4. Agent 定义，包括name、instructions、handoffs、tools
5. Runner执行代码，根据选择生成同步或异步版本

代码生成逻辑示例:

- 对于每个Function Tool节点，生成@function_tool装饰器函数
- 对于每个Agent节点，生成Agent实例
- 对于连接到Agent的Function节点，添加到Agent的tools参数
- 对于连接到Agent的其他Agent节点，添加到handoffs参数
- 对于Runner节点，生成对应的Runner.run或Runner.run_sync代码
- 如果有异步执行，添加asyncio.run(main())代码

## 6. 用户交互设计

1. **组件拖放**:
   - 用户从左侧面板拖动组件到画布
   - 拖放时应显示可放置位置的提示

2. **节点连接**:
   - 节点有输入/输出连接点
   - 用户可以拖动连接线连接不同节点
   - 连接时应验证是否为有效连接

3. **节点配置**:
   - 点击节点可选中并显示配置选项
   - 节点可展开/折叠以显示/隐藏详细选项
   - 节点大小应根据内容自适应

4. **代码生成**:
   - 点击"生成代码"按钮显示弹窗
   - 代码应格式化并高亮显示
   - 提供复制到剪贴板功能

## 7. 样式设计

整体风格建议:
- 简洁现代的界面
- 柔和的色彩方案
- 充分的留白
- 清晰的视觉层次

节点颜色编码:
- Agent: 蓝色(#3498db)顶部边框
- Runner: 红色(#e74c3c)顶部边框
- Function Tool: 黄色(#f39c12)顶部边框
- Guardrail: 紫色(#9b59b6)顶部边框

## 8. 示例参考

参考下面的Agent代码示例，实现相应的可视化表示:

### 示例1: 基本Agent

python
from agents import Agent, Runner

agent = Agent(name="Assistant", instructions="You are a helpful assistant")
result = Runner.run_sync(agent, "Write a haiku about recursion in programming.")
print(result.final_output)

### 示例2: Handoffs

python
from agents import Agent, Runner
import asyncio

spanish_agent = Agent(
    name="Spanish agent",
    instructions="You only speak Spanish.",
)

english_agent = Agent(
    name="English agent",
    instructions="You only speak English",
)

triage_agent = Agent(
    name="Triage agent",
    instructions="Handoff to the appropriate agent based on the language of the request.",
    handoffs=[spanish_agent, english_agent],
)

async def main():
    result = await Runner.run(triage_agent, input="Hola, ¿cómo estás?")
    print(result.final_output)

if __name__ == "__main__":
    asyncio.run(main())

### 示例3: Function Tools

python
import asyncio
from agents import Agent, Runner, function_tool

@function_tool
def get_weather(city: str) -> str:
    return f"The weather in {city} is sunny."

agent = Agent(
    name="Hello world",
    instructions="You are a helpful agent.",
    tools=[get_weather],
)

async def main():
    result = await Runner.run(agent, input="What's the weather in Tokyo?")
    print(result.final_output)

if __name__ == "__main__":
    asyncio.run(main())

### 示例4: Guardrails

python
from agents import Agent, InputGuardrail, GuardrailFunctionOutput, Runner
from pydantic import BaseModel
import asyncio

class HomeworkOutput(BaseModel):
    is_homework: bool
    reasoning: str

guardrail_agent = Agent(
    name="Guardrail check",
    instructions="Check if the user is asking about homework.",
    output_type=HomeworkOutput,
)

async def homework_guardrail(ctx, agent, input_data):
    result = await Runner.run(guardrail_agent, input_data, context=ctx.context)
    final_output = result.final_output_as(HomeworkOutput)
    return GuardrailFunctionOutput(
        output_info=final_output,
        tripwire_triggered=not final_output.is_homework,
    )

math_tutor_agent = Agent(
    name="Math Tutor",
    handoff_description="Specialist agent for math questions",
    instructions="You provide help with math problems. Explain your reasoning at each step and include examples",
)

history_tutor_agent = Agent(
    name="History Tutor",
    handoff_description="Specialist agent for historical questions",
    instructions="You provide assistance with historical queries. Explain important events and context clearly.",
)

triage_agent = Agent(
    name="Triage Agent",
    instructions="You determine which agent to use based on the user's homework question",
    handoffs=[history_tutor_agent, math_tutor_agent],
    input_guardrails=[
        InputGuardrail(guardrail_function=homework_guardrail),
    ],
)

async def main():
    result = await Runner.run(triage_agent, "who was the first president of the united states?")
    print(result.final_output)

    result = await Runner.run(triage_agent, "what is life")
    print(result.final_output)

if __name__ == "__main__":
    asyncio.run(main())

### 示例5: Specialist Agents

python
from agents import Agent, Runner
import asyncio

history_tutor_agent = Agent(
    name="History Tutor",
    handoff_description="Specialist agent for historical questions",
    instructions="You provide assistance with historical queries. Explain important events and context clearly.",
)

math_tutor_agent = Agent(
    name="Math Tutor",
    handoff_description="Specialist agent for math questions",
    instructions="You provide help with math problems. Explain your reasoning at each step and include examples",
)

triage_agent = Agent(
    name="Triage Agent",
    instructions="You determine which agent to use based on the user's homework question",
    handoffs=[history_tutor_agent, math_tutor_agent]
)

async def main():
    result = await Runner.run(triage_agent, "What is the capital of France?")
    print(result.final_output)

if __name__ == "__main__":
    asyncio.run(main())

## 9. 扩展功能（如果可能）

如果时间和资源允许，可以考虑以下扩展功能:

1. **保存/加载**: 允许用户保存工作流为JSON并重新加载
2. **示例模板**: 提供几个预设的工作流模板
3. **实时预览**: 在编辑过程中实时更新生成的代码
4. **导出功能**: 允许导出Python文件
5. **错误验证**: 检测并提示工作流中的潜在问题
6. **调试视图**: 提供简单的调试界面，显示工作流执行路径
7. **导入功能**: 从现有Python代码导入并创建可视化工作流

请根据以上需求实现这个OpenAI Agents可视化工作流设计器。设计应该直观易用，让用户能够轻松创建复杂的Agent工作流并生成可用的Python代码。
